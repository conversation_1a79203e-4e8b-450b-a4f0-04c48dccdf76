package com.tqhit.battery.one.service.animation;

import android.content.Context;
import com.tqhit.battery.one.manager.animation.AnimationFileManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class AnimationPreloader_Factory implements Factory<AnimationPreloader> {
  private final Provider<Context> contextProvider;

  private final Provider<AnimationFileManager> fileManagerProvider;

  public AnimationPreloader_Factory(Provider<Context> contextProvider,
      Provider<AnimationFileManager> fileManagerProvider) {
    this.contextProvider = contextProvider;
    this.fileManagerProvider = fileManagerProvider;
  }

  @Override
  public AnimationPreloader get() {
    return newInstance(contextProvider.get(), fileManagerProvider.get());
  }

  public static AnimationPreloader_Factory create(Provider<Context> contextProvider,
      Provider<AnimationFileManager> fileManagerProvider) {
    return new AnimationPreloader_Factory(contextProvider, fileManagerProvider);
  }

  public static AnimationPreloader newInstance(Context context, AnimationFileManager fileManager) {
    return new AnimationPreloader(context, fileManager);
  }
}
