package com.tqhit.battery.one.service.thumbnail;

import android.content.Context;
import com.tqhit.battery.one.manager.thumbnail.ThumbnailFileManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class ThumbnailPreloader_Factory implements Factory<ThumbnailPreloader> {
  private final Provider<Context> contextProvider;

  private final Provider<ThumbnailFileManager> fileManagerProvider;

  public ThumbnailPreloader_Factory(Provider<Context> contextProvider,
      Provider<ThumbnailFileManager> fileManagerProvider) {
    this.contextProvider = contextProvider;
    this.fileManagerProvider = fileManagerProvider;
  }

  @Override
  public ThumbnailPreloader get() {
    return newInstance(contextProvider.get(), fileManagerProvider.get());
  }

  public static ThumbnailPreloader_Factory create(Provider<Context> contextProvider,
      Provider<ThumbnailFileManager> fileManagerProvider) {
    return new ThumbnailPreloader_Factory(contextProvider, fileManagerProvider);
  }

  public static ThumbnailPreloader newInstance(Context context, ThumbnailFileManager fileManager) {
    return new ThumbnailPreloader(context, fileManager);
  }
}
