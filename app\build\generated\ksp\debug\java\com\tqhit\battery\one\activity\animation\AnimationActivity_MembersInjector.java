package com.tqhit.battery.one.activity.animation;

import com.tqhit.adlib.sdk.firebase.FirebaseRemoteConfigHelper;
import com.tqhit.battery.one.ads.core.ApplovinInterstitialAdManager;
import com.tqhit.battery.one.ads.core.ApplovinRewardedAdManager;
import com.tqhit.battery.one.repository.AppRepository;
import com.tqhit.battery.one.utils.VideoUtils;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import javax.annotation.processing.Generated;

@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class AnimationActivity_MembersInjector implements MembersInjector<AnimationActivity> {
  private final Provider<FirebaseRemoteConfigHelper> remoteConfigHelperProvider;

  private final Provider<ApplovinRewardedAdManager> applovinRewardedAdManagerProvider;

  private final Provider<ApplovinInterstitialAdManager> applovinInterstitialAdManagerProvider;

  private final Provider<AppRepository> appRepositoryProvider;

  private final Provider<VideoUtils> videoUtilsProvider;

  public AnimationActivity_MembersInjector(
      Provider<FirebaseRemoteConfigHelper> remoteConfigHelperProvider,
      Provider<ApplovinRewardedAdManager> applovinRewardedAdManagerProvider,
      Provider<ApplovinInterstitialAdManager> applovinInterstitialAdManagerProvider,
      Provider<AppRepository> appRepositoryProvider, Provider<VideoUtils> videoUtilsProvider) {
    this.remoteConfigHelperProvider = remoteConfigHelperProvider;
    this.applovinRewardedAdManagerProvider = applovinRewardedAdManagerProvider;
    this.applovinInterstitialAdManagerProvider = applovinInterstitialAdManagerProvider;
    this.appRepositoryProvider = appRepositoryProvider;
    this.videoUtilsProvider = videoUtilsProvider;
  }

  public static MembersInjector<AnimationActivity> create(
      Provider<FirebaseRemoteConfigHelper> remoteConfigHelperProvider,
      Provider<ApplovinRewardedAdManager> applovinRewardedAdManagerProvider,
      Provider<ApplovinInterstitialAdManager> applovinInterstitialAdManagerProvider,
      Provider<AppRepository> appRepositoryProvider, Provider<VideoUtils> videoUtilsProvider) {
    return new AnimationActivity_MembersInjector(remoteConfigHelperProvider, applovinRewardedAdManagerProvider, applovinInterstitialAdManagerProvider, appRepositoryProvider, videoUtilsProvider);
  }

  @Override
  public void injectMembers(AnimationActivity instance) {
    injectRemoteConfigHelper(instance, remoteConfigHelperProvider.get());
    injectApplovinRewardedAdManager(instance, applovinRewardedAdManagerProvider.get());
    injectApplovinInterstitialAdManager(instance, applovinInterstitialAdManagerProvider.get());
    injectAppRepository(instance, appRepositoryProvider.get());
    injectVideoUtils(instance, videoUtilsProvider.get());
  }

  @InjectedFieldSignature("com.tqhit.battery.one.activity.animation.AnimationActivity.remoteConfigHelper")
  public static void injectRemoteConfigHelper(AnimationActivity instance,
      FirebaseRemoteConfigHelper remoteConfigHelper) {
    instance.remoteConfigHelper = remoteConfigHelper;
  }

  @InjectedFieldSignature("com.tqhit.battery.one.activity.animation.AnimationActivity.applovinRewardedAdManager")
  public static void injectApplovinRewardedAdManager(AnimationActivity instance,
      ApplovinRewardedAdManager applovinRewardedAdManager) {
    instance.applovinRewardedAdManager = applovinRewardedAdManager;
  }

  @InjectedFieldSignature("com.tqhit.battery.one.activity.animation.AnimationActivity.applovinInterstitialAdManager")
  public static void injectApplovinInterstitialAdManager(AnimationActivity instance,
      ApplovinInterstitialAdManager applovinInterstitialAdManager) {
    instance.applovinInterstitialAdManager = applovinInterstitialAdManager;
  }

  @InjectedFieldSignature("com.tqhit.battery.one.activity.animation.AnimationActivity.appRepository")
  public static void injectAppRepository(AnimationActivity instance, AppRepository appRepository) {
    instance.appRepository = appRepository;
  }

  @InjectedFieldSignature("com.tqhit.battery.one.activity.animation.AnimationActivity.videoUtils")
  public static void injectVideoUtils(AnimationActivity instance, VideoUtils videoUtils) {
    instance.videoUtils = videoUtils;
  }
}
