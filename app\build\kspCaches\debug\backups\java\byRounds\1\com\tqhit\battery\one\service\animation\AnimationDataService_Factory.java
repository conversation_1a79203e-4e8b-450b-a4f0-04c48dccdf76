package com.tqhit.battery.one.service.animation;

import com.google.gson.Gson;
import com.tqhit.adlib.sdk.firebase.FirebaseRemoteConfigHelper;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class AnimationDataService_Factory implements Factory<AnimationDataService> {
  private final Provider<FirebaseRemoteConfigHelper> remoteConfigHelperProvider;

  private final Provider<Gson> gsonProvider;

  public AnimationDataService_Factory(
      Provider<FirebaseRemoteConfigHelper> remoteConfigHelperProvider,
      Provider<Gson> gsonProvider) {
    this.remoteConfigHelperProvider = remoteConfigHelperProvider;
    this.gsonProvider = gsonProvider;
  }

  @Override
  public AnimationDataService get() {
    return newInstance(remoteConfigHelperProvider.get(), gsonProvider.get());
  }

  public static AnimationDataService_Factory create(
      Provider<FirebaseRemoteConfigHelper> remoteConfigHelperProvider,
      Provider<Gson> gsonProvider) {
    return new AnimationDataService_Factory(remoteConfigHelperProvider, gsonProvider);
  }

  public static AnimationDataService newInstance(FirebaseRemoteConfigHelper remoteConfigHelper,
      Gson gson) {
    return new AnimationDataService(remoteConfigHelper, gson);
  }
}
