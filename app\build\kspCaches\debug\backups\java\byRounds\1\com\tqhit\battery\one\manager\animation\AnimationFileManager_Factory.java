package com.tqhit.battery.one.manager.animation;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class AnimationFileManager_Factory implements Factory<AnimationFileManager> {
  private final Provider<Context> contextProvider;

  public AnimationFileManager_Factory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public AnimationFileManager get() {
    return newInstance(contextProvider.get());
  }

  public static AnimationFileManager_Factory create(Provider<Context> contextProvider) {
    return new AnimationFileManager_Factory(contextProvider);
  }

  public static AnimationFileManager newInstance(Context context) {
    return new AnimationFileManager(context);
  }
}
