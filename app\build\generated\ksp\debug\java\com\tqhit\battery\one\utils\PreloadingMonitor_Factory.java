package com.tqhit.battery.one.utils;

import com.tqhit.battery.one.repository.AnimationPreloadingRepository;
import com.tqhit.battery.one.repository.ThumbnailPreloadingRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class PreloadingMonitor_Factory implements Factory<PreloadingMonitor> {
  private final Provider<AnimationPreloadingRepository> animationPreloadingRepositoryProvider;

  private final Provider<ThumbnailPreloadingRepository> thumbnailPreloadingRepositoryProvider;

  public PreloadingMonitor_Factory(
      Provider<AnimationPreloadingRepository> animationPreloadingRepositoryProvider,
      Provider<ThumbnailPreloadingRepository> thumbnailPreloadingRepositoryProvider) {
    this.animationPreloadingRepositoryProvider = animationPreloadingRepositoryProvider;
    this.thumbnailPreloadingRepositoryProvider = thumbnailPreloadingRepositoryProvider;
  }

  @Override
  public PreloadingMonitor get() {
    return newInstance(animationPreloadingRepositoryProvider.get(), thumbnailPreloadingRepositoryProvider.get());
  }

  public static PreloadingMonitor_Factory create(
      Provider<AnimationPreloadingRepository> animationPreloadingRepositoryProvider,
      Provider<ThumbnailPreloadingRepository> thumbnailPreloadingRepositoryProvider) {
    return new PreloadingMonitor_Factory(animationPreloadingRepositoryProvider, thumbnailPreloadingRepositoryProvider);
  }

  public static PreloadingMonitor newInstance(
      AnimationPreloadingRepository animationPreloadingRepository,
      ThumbnailPreloadingRepository thumbnailPreloadingRepository) {
    return new PreloadingMonitor(animationPreloadingRepository, thumbnailPreloadingRepository);
  }
}
