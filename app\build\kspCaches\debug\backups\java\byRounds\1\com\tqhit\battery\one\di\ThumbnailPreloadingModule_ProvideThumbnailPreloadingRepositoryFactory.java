package com.tqhit.battery.one.di;

import com.google.gson.Gson;
import com.tqhit.adlib.sdk.data.local.PreferencesHelper;
import com.tqhit.battery.one.manager.thumbnail.ThumbnailFileManager;
import com.tqhit.battery.one.repository.ThumbnailPreloadingRepository;
import com.tqhit.battery.one.service.thumbnail.ThumbnailPreloader;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class ThumbnailPreloadingModule_ProvideThumbnailPreloadingRepositoryFactory implements Factory<ThumbnailPreloadingRepository> {
  private final Provider<ThumbnailPreloader> thumbnailPreloaderProvider;

  private final Provider<ThumbnailFileManager> fileManagerProvider;

  private final Provider<PreferencesHelper> preferencesHelperProvider;

  private final Provider<Gson> gsonProvider;

  public ThumbnailPreloadingModule_ProvideThumbnailPreloadingRepositoryFactory(
      Provider<ThumbnailPreloader> thumbnailPreloaderProvider,
      Provider<ThumbnailFileManager> fileManagerProvider,
      Provider<PreferencesHelper> preferencesHelperProvider, Provider<Gson> gsonProvider) {
    this.thumbnailPreloaderProvider = thumbnailPreloaderProvider;
    this.fileManagerProvider = fileManagerProvider;
    this.preferencesHelperProvider = preferencesHelperProvider;
    this.gsonProvider = gsonProvider;
  }

  @Override
  public ThumbnailPreloadingRepository get() {
    return provideThumbnailPreloadingRepository(thumbnailPreloaderProvider.get(), fileManagerProvider.get(), preferencesHelperProvider.get(), gsonProvider.get());
  }

  public static ThumbnailPreloadingModule_ProvideThumbnailPreloadingRepositoryFactory create(
      Provider<ThumbnailPreloader> thumbnailPreloaderProvider,
      Provider<ThumbnailFileManager> fileManagerProvider,
      Provider<PreferencesHelper> preferencesHelperProvider, Provider<Gson> gsonProvider) {
    return new ThumbnailPreloadingModule_ProvideThumbnailPreloadingRepositoryFactory(thumbnailPreloaderProvider, fileManagerProvider, preferencesHelperProvider, gsonProvider);
  }

  public static ThumbnailPreloadingRepository provideThumbnailPreloadingRepository(
      ThumbnailPreloader thumbnailPreloader, ThumbnailFileManager fileManager,
      PreferencesHelper preferencesHelper, Gson gson) {
    return Preconditions.checkNotNullFromProvides(ThumbnailPreloadingModule.INSTANCE.provideThumbnailPreloadingRepository(thumbnailPreloader, fileManager, preferencesHelper, gson));
  }
}
