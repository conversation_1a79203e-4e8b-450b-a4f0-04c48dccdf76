package com.tqhit.battery.one.service.thumbnail;

import com.tqhit.battery.one.service.animation.AnimationDataService;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class ThumbnailDataService_Factory implements Factory<ThumbnailDataService> {
  private final Provider<AnimationDataService> animationDataServiceProvider;

  public ThumbnailDataService_Factory(Provider<AnimationDataService> animationDataServiceProvider) {
    this.animationDataServiceProvider = animationDataServiceProvider;
  }

  @Override
  public ThumbnailDataService get() {
    return newInstance(animationDataServiceProvider.get());
  }

  public static ThumbnailDataService_Factory create(
      Provider<AnimationDataService> animationDataServiceProvider) {
    return new ThumbnailDataService_Factory(animationDataServiceProvider);
  }

  public static ThumbnailDataService newInstance(AnimationDataService animationDataService) {
    return new ThumbnailDataService(animationDataService);
  }
}
