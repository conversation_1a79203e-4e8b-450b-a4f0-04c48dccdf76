package com.tqhit.battery.one.di;

import android.content.Context;
import com.tqhit.battery.one.manager.thumbnail.ThumbnailFileManager;
import com.tqhit.battery.one.service.thumbnail.ThumbnailPreloader;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class ThumbnailPreloadingModule_ProvideThumbnailPreloaderFactory implements Factory<ThumbnailPreloader> {
  private final Provider<Context> contextProvider;

  private final Provider<ThumbnailFileManager> fileManagerProvider;

  public ThumbnailPreloadingModule_ProvideThumbnailPreloaderFactory(
      Provider<Context> contextProvider, Provider<ThumbnailFileManager> fileManagerProvider) {
    this.contextProvider = contextProvider;
    this.fileManagerProvider = fileManagerProvider;
  }

  @Override
  public ThumbnailPreloader get() {
    return provideThumbnailPreloader(contextProvider.get(), fileManagerProvider.get());
  }

  public static ThumbnailPreloadingModule_ProvideThumbnailPreloaderFactory create(
      Provider<Context> contextProvider, Provider<ThumbnailFileManager> fileManagerProvider) {
    return new ThumbnailPreloadingModule_ProvideThumbnailPreloaderFactory(contextProvider, fileManagerProvider);
  }

  public static ThumbnailPreloader provideThumbnailPreloader(Context context,
      ThumbnailFileManager fileManager) {
    return Preconditions.checkNotNullFromProvides(ThumbnailPreloadingModule.INSTANCE.provideThumbnailPreloader(context, fileManager));
  }
}
