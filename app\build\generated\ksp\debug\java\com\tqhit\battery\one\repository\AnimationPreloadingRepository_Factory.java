package com.tqhit.battery.one.repository;

import com.google.gson.Gson;
import com.tqhit.adlib.sdk.data.local.PreferencesHelper;
import com.tqhit.battery.one.manager.animation.AnimationFileManager;
import com.tqhit.battery.one.service.animation.AnimationPreloader;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class AnimationPreloadingRepository_Factory implements Factory<AnimationPreloadingRepository> {
  private final Provider<PreferencesHelper> preferencesHelperProvider;

  private final Provider<AnimationPreloader> animationPreloaderProvider;

  private final Provider<AnimationFileManager> fileManagerProvider;

  private final Provider<Gson> gsonProvider;

  public AnimationPreloadingRepository_Factory(
      Provider<PreferencesHelper> preferencesHelperProvider,
      Provider<AnimationPreloader> animationPreloaderProvider,
      Provider<AnimationFileManager> fileManagerProvider, Provider<Gson> gsonProvider) {
    this.preferencesHelperProvider = preferencesHelperProvider;
    this.animationPreloaderProvider = animationPreloaderProvider;
    this.fileManagerProvider = fileManagerProvider;
    this.gsonProvider = gsonProvider;
  }

  @Override
  public AnimationPreloadingRepository get() {
    return newInstance(preferencesHelperProvider.get(), animationPreloaderProvider.get(), fileManagerProvider.get(), gsonProvider.get());
  }

  public static AnimationPreloadingRepository_Factory create(
      Provider<PreferencesHelper> preferencesHelperProvider,
      Provider<AnimationPreloader> animationPreloaderProvider,
      Provider<AnimationFileManager> fileManagerProvider, Provider<Gson> gsonProvider) {
    return new AnimationPreloadingRepository_Factory(preferencesHelperProvider, animationPreloaderProvider, fileManagerProvider, gsonProvider);
  }

  public static AnimationPreloadingRepository newInstance(PreferencesHelper preferencesHelper,
      AnimationPreloader animationPreloader, AnimationFileManager fileManager, Gson gson) {
    return new AnimationPreloadingRepository(preferencesHelper, animationPreloader, fileManager, gson);
  }
}
