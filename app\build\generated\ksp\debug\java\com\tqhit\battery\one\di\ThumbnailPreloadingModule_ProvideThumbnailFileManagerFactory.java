package com.tqhit.battery.one.di;

import android.content.Context;
import com.tqhit.battery.one.manager.thumbnail.ThumbnailFileManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class ThumbnailPreloadingModule_ProvideThumbnailFileManagerFactory implements Factory<ThumbnailFileManager> {
  private final Provider<Context> contextProvider;

  public ThumbnailPreloadingModule_ProvideThumbnailFileManagerFactory(
      Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public ThumbnailFileManager get() {
    return provideThumbnailFileManager(contextProvider.get());
  }

  public static ThumbnailPreloadingModule_ProvideThumbnailFileManagerFactory create(
      Provider<Context> contextProvider) {
    return new ThumbnailPreloadingModule_ProvideThumbnailFileManagerFactory(contextProvider);
  }

  public static ThumbnailFileManager provideThumbnailFileManager(Context context) {
    return Preconditions.checkNotNullFromProvides(ThumbnailPreloadingModule.INSTANCE.provideThumbnailFileManager(context));
  }
}
