package com.tqhit.battery.one.utils;

import android.content.Context;
import com.tqhit.battery.one.repository.AnimationPreloadingRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class VideoUtils_Factory implements Factory<VideoUtils> {
  private final Provider<Context> contextProvider;

  private final Provider<AnimationPreloadingRepository> animationPreloadingRepositoryProvider;

  public VideoUtils_Factory(Provider<Context> contextProvider,
      Provider<AnimationPreloadingRepository> animationPreloadingRepositoryProvider) {
    this.contextProvider = contextProvider;
    this.animationPreloadingRepositoryProvider = animationPreloadingRepositoryProvider;
  }

  @Override
  public VideoUtils get() {
    return newInstance(contextProvider.get(), animationPreloadingRepositoryProvider.get());
  }

  public static VideoUtils_Factory create(Provider<Context> contextProvider,
      Provider<AnimationPreloadingRepository> animationPreloadingRepositoryProvider) {
    return new VideoUtils_Factory(contextProvider, animationPreloadingRepositoryProvider);
  }

  public static VideoUtils newInstance(Context context,
      AnimationPreloadingRepository animationPreloadingRepository) {
    return new VideoUtils(context, animationPreloadingRepository);
  }
}
